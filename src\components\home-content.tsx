"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { HomeContentProps } from "@/types";
import { comingSoon } from "@/utils/helpers";
import { useTransition } from "react";
import { createNewPage } from "@/actions/create-page";
import { useRouter } from "next/navigation";

export default function HomeContent({ pages, userId }: HomeContentProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const handleNewPage = async () => {
    startTransition(async () => {
      if (!userId?.id) return;
      const page = await createNewPage(userId.id);
      router.push(`/dashboard/pages/${page.id}`);
    });
  };

  return (
    <main className="min-h-screen flex flex-col items-center justify-start px-6 sm:px-4 py-12">
      <div className="flex flex-col items-center justify-center gap-3">
        <motion.div
          className="w-8 sm:w-10 shadow-md bg-primary aspect-square rounded-lg"
          animate={{
            scale: [0.5, 1, 1, 0.5],
            rotate: [0, 90, 90, 0],
            borderRadius: ["10%", "10%", "50%", "10%"],
          }}
          transition={{ duration: 4, ease: "easeInOut", repeat: Infinity }}
        />
        <h1 className="font-bold text-2xl sm:text-3xl text-center text-foreground">
          Welcome back, {userId.username}!
        </h1>
        <p className="text-muted-foreground text-center text-sm">
          Your minimal block-based editor for notes, ideas & tasks.
        </p>
      </div>

      <div className="flex flex-wrap gap-4 mt-8 w-full justify-center">
        <Button onClick={handleNewPage} size="lg" disabled={isPending} className="w-full sm:w-auto">
          {isPending ? "Creating..." : "New Page"}
        </Button>
        <Button onClick={comingSoon} variant="outline" size="lg" className="w-full sm:w-auto">
          Browse Templates
        </Button>
      </div>

      <div className="w-full max-w-4xl mt-12">
        <h2 className="text-lg font-semibold mb-4">Recent Pages</h2>

        {pages.length === 0 ? (
          <p className="text-muted-foreground text-sm">
            You have no pages yet. Create your first one!
          </p>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {pages.map((page) => (
              <div
                key={page.id}
                className="bg-muted rounded-lg p-4 hover:shadow-md transition cursor-pointer"
              >
                <h3 className="font-medium">{page.title || "Untitled"}</h3>
                <p className="text-xs text-muted-foreground">
                  Updated {new Date(page.updated_at).toLocaleDateString()}
                </p>
              </div>
            ))}
          </div>
        )}
      </div>
    </main>
  );
}
